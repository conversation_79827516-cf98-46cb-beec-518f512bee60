<?php
/**
 * Common Functions
 *
 * This file contains common utility functions used throughout the application.
 */

/**
 * Get the current language
 *
 * @return string The current language code (en or ar)
 */
function getCurrentLanguage() {
    return isset($_SESSION['language']) ? $_SESSION['language'] : 'en';
}

/**
 * Set the current language
 *
 * @param string $language The language code (en or ar)
 * @return void
 */
function setLanguage($language) {
    if (in_array($language, ['en', 'ar'])) {
        $_SESSION['language'] = $language;
    }
}

/**
 * Get a translated string
 *
 * @param string $key The translation key
 * @param array $params Optional parameters for string formatting
 * @return string The translated string
 */
function __($key, $params = []) {
    $language = getCurrentLanguage();
    $translations = include __DIR__ . "/../languages/{$language}.php";

    if (isset($translations[$key])) {
        $text = $translations[$key];

        // Replace parameters in the string
        if (!empty($params)) {
            foreach ($params as $i => $param) {
                $text = str_replace("{{$i}}", $param, $text);
            }
        }

        return $text;
    }

    return $key;
}

/**
 * Redirect to a URL
 *
 * @param string $url The URL to redirect to (can be relative or absolute)
 * @return void
 */
function redirect($url) {
    // If the URL doesn't start with http:// or https://, make it relative to base URL
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = getBaseUrl() . '/' . ltrim($url, '/');
    }

    header("Location: {$url}");
    exit;
}

/**
 * Check if the current request is an AJAX request
 *
 * @return bool True if the request is an AJAX request, false otherwise
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Generate a random string
 *
 * @param int $length The length of the string
 * @return string The random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

/**
 * Format a date
 *
 * @param string $date The date to format
 * @param string $format The format to use
 * @return string The formatted date
 */
function formatDate($date, $format = 'Y-m-d') {
    return date($format, strtotime($date));
}

/**
 * Sanitize input
 *
 * @param string $input The input to sanitize
 * @return string The sanitized input
 */
function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }

    // Remove null bytes
    $input = str_replace("\0", '', $input);

    // Trim whitespace
    $input = trim($input);

    // Convert special characters to HTML entities
    return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
}

/**
 * Sanitize input for database (additional layer)
 *
 * @param string $input The input to sanitize
 * @return string The sanitized input
 */
function sanitizeForDatabase($input) {
    if (is_array($input)) {
        return array_map('sanitizeForDatabase', $input);
    }

    // First apply basic sanitization
    $input = sanitize($input);

    // Remove any remaining dangerous characters
    $input = preg_replace('/[<>"\']/', '', $input);

    return $input;
}

/**
 * Generate a QR code for a device
 *
 * @param int $deviceId The device ID
 * @param string $serialNumber The device serial number
 * @return string The path to the QR code image
 */
function generateQRCode($deviceId, $serialNumber) {
    // Check if GD extension is available
    if (!extension_loaded('gd')) {
        error_log("GD extension is not available. QR code generation failed.");
        return false;
    }

    // Include the QR code library
    require_once __DIR__ . '/../vendor/phpqrcode/qrlib.php';

    // Create the QR code data
    $qrData = json_encode([
        'id' => $deviceId,
        'serial' => $serialNumber,
        'url' => 'ticket/create/' . $deviceId
    ]);

    // Generate a unique filename
    $filename = 'qrcode_' . $deviceId . '_' . time() . '.png';
    $filepath = __DIR__ . '/../uploads/qrcodes/' . $filename;

    // Generate the QR code
    QRcode::png($qrData, $filepath, 'L', 10, 2);

    return 'uploads/qrcodes/' . $filename;
}



/**
 * Get the current user
 *
 * @return array|null The current user or null if not logged in
 */
function getCurrentUser() {
    return isset($_SESSION['user']) ? $_SESSION['user'] : null;
}

/**
 * Check if a user is logged in
 *
 * @return bool True if the user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user']);
}

/**
 * Set a flash message
 *
 * @param string $type The message type (success, error, warning, info)
 * @param string $message The message
 * @return void
 */
function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Get and clear the flash message
 *
 * @return array|null The flash message or null if none exists
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }

    return null;
}

/**
 * Get the base URL of the application
 *
 * @return string The base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];

    // Get the directory path of the current script
    $scriptPath = $_SERVER['SCRIPT_NAME'];
    $path = dirname($scriptPath);

    // Handle different server configurations
    if ($path === '/' || $path === '\\') {
        $path = '';
    }

    // Remove trailing slash
    $path = rtrim($path, '/\\');

    return $protocol . '://' . $host . $path;
}

/**
 * Get the current page URL
 *
 * @return string The current page URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];

    return $protocol . '://' . $host . $uri;
}

/**
 * Check if the current page is the active page
 *
 * @param string $page The page to check
 * @return bool True if the current page is the active page, false otherwise
 */
function isActivePage($page) {
    $currentPage = basename($_SERVER['PHP_SELF']);
    return $currentPage === $page;
}

/**
 * Get the client IP address
 *
 * @return string The client IP address
 */
function getClientIp() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    } else {
        $ip = '127.0.0.1'; // Default fallback
    }

    return $ip;
}

/**
 * Get status color for badges
 *
 * @param string $status The status
 * @return string The color class
 */
function getStatusColor($status) {
    switch ($status) {
        case 'operational':
            return 'success';
        case 'under_maintenance':
            return 'warning';
        case 'out_of_order':
            return 'danger';
        case 'retired':
            return 'secondary';
        default:
            return 'secondary';
    }
}

/**
 * Get maintenance status color for badges
 *
 * @param string $status The maintenance status
 * @return string The color class
 */
function getMaintenanceStatusColor($status) {
    switch ($status) {
        case 'scheduled':
            return 'primary';
        case 'in_progress':
            return 'warning';
        case 'completed':
            return 'success';
        case 'cancelled':
            return 'secondary';
        case 'overdue':
            return 'danger';
        default:
            return 'secondary';
    }
}

/**
 * Get priority color for badges
 *
 * @param string $priority The priority level
 * @return string The color class
 */
function getPriorityColor($priority) {
    switch ($priority) {
        case 'low':
            return 'success';
        case 'medium':
            return 'warning';
        case 'high':
            return 'danger';
        case 'urgent':
            return 'dark';
        default:
            return 'secondary';
    }
}

/**
 * Get ticket status color for badges
 *
 * @param string $status The ticket status
 * @return string The color class
 */
function getTicketStatusColor($status) {
    switch ($status) {
        case 'open':
            return 'primary';
        case 'in_progress':
            return 'warning';
        case 'resolved':
            return 'success';
        case 'closed':
            return 'secondary';
        default:
            return 'secondary';
    }
}

/**
 * Get role color for badges
 *
 * @param string $role The user role
 * @return string The color class
 */
function getRoleColor($role) {
    switch ($role) {
        case 'admin':
            return 'danger';
        case 'manager':
            return 'warning';
        case 'engineer':
            return 'primary';
        case 'technician':
            return 'info';
        case 'staff':
            return 'info';
        case 'user':
            return 'secondary';
        default:
            return 'secondary';
    }
}

/**
 * Get notification icon
 *
 * @param string $type The notification type
 * @return string The icon class
 */
function getNotificationIcon($type) {
    switch ($type) {
        case 'maintenance':
            return 'wrench';
        case 'ticket':
            return 'exclamation-triangle';
        case 'system':
            return 'cog';
        case 'warning':
            return 'exclamation-circle';
        case 'info':
            return 'info-circle';
        default:
            return 'bell';
    }
}

/**
 * Get notification color
 *
 * @param string $type The notification type
 * @return string The color class
 */
function getNotificationColor($type) {
    switch ($type) {
        case 'maintenance':
            return 'warning';
        case 'ticket':
            return 'danger';
        case 'system':
            return 'info';
        case 'warning':
            return 'warning';
        case 'info':
            return 'info';
        default:
            return 'primary';
    }
}

/**
 * Log an action
 *
 * @param string $action The action to log
 * @param string $details The details of the action
 * @return void
 */
function logAction($action, $details = '') {
    global $pdo;

    $userId = isset($_SESSION['user']) ? $_SESSION['user']['id'] : 0;
    $ip = getClientIp();

    $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)");
    $stmt->execute([$userId, $action, $details, $ip]);
}

/**
 * Get recent user activity
 *
 * @param int $userId The user ID
 * @param int $limit The number of activities to return
 * @return array The recent activities
 */
function getRecentUserActivity($userId, $limit = 10) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT action, details, created_at,
                   CASE
                       WHEN action = 'login' THEN 'user'
                       WHEN action = 'logout' THEN 'sign-out-alt'
                       WHEN action = 'create_ticket' THEN 'ticket-alt'
                       WHEN action = 'update_ticket' THEN 'edit'
                       WHEN action = 'create_maintenance' THEN 'tools'
                       WHEN action = 'update_maintenance' THEN 'wrench'
                       WHEN action = 'update_profile' THEN 'user-edit'
                       WHEN action = 'change_password' THEN 'key'
                       ELSE 'info-circle'
                   END as icon,
                   CASE
                       WHEN action = 'login' THEN 'success'
                       WHEN action = 'logout' THEN 'secondary'
                       WHEN action = 'create_ticket' THEN 'danger'
                       WHEN action = 'update_ticket' THEN 'warning'
                       WHEN action = 'create_maintenance' THEN 'info'
                       WHEN action = 'update_maintenance' THEN 'primary'
                       WHEN action = 'update_profile' THEN 'info'
                       WHEN action = 'change_password' THEN 'warning'
                       ELSE 'secondary'
                   END as color
            FROM activity_logs
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ?
        ");

        $stmt->execute([$userId, $limit]);
        $activities = $stmt->fetchAll();

        // Format activities for display
        $formattedActivities = [];
        foreach ($activities as $activity) {
            $formattedActivities[] = [
                'type' => $activity['action'],
                'title' => formatActivityTitle($activity['action']),
                'description' => $activity['details'] ?: formatActivityDescription($activity['action']),
                'created_at' => $activity['created_at'],
                'icon' => $activity['icon'],
                'color' => $activity['color']
            ];
        }

        return $formattedActivities;
    } catch (PDOException $e) {
        error_log("Get Recent User Activity Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Format activity title
 *
 * @param string $action The action
 * @return string The formatted title
 */
function formatActivityTitle($action) {
    switch ($action) {
        case 'login':
            return __('logged_in');
        case 'logout':
            return __('logged_out');
        case 'create_ticket':
            return __('created_ticket');
        case 'update_ticket':
            return __('updated_ticket');
        case 'create_maintenance':
            return __('created_maintenance');
        case 'update_maintenance':
            return __('updated_maintenance');
        case 'update_profile':
            return __('updated_profile');
        case 'change_password':
            return __('changed_password');
        default:
            return ucfirst(str_replace('_', ' ', $action));
    }
}

/**
 * Format activity description
 *
 * @param string $action The action
 * @return string The formatted description
 */
function formatActivityDescription($action) {
    switch ($action) {
        case 'login':
            return __('user_logged_into_system');
        case 'logout':
            return __('user_logged_out_of_system');
        case 'create_ticket':
            return __('user_created_new_ticket');
        case 'update_ticket':
            return __('user_updated_ticket');
        case 'create_maintenance':
            return __('user_created_maintenance_schedule');
        case 'update_maintenance':
            return __('user_updated_maintenance_record');
        case 'update_profile':
            return __('user_updated_profile_information');
        case 'change_password':
            return __('user_changed_password');
        default:
            return __('user_performed_action');
    }
}

/**
 * Get activity icon
 *
 * @param string $type The activity type
 * @return string The icon class
 */
function getActivityIcon($type) {
    switch ($type) {
        case 'login':
            return 'user';
        case 'logout':
            return 'sign-out-alt';
        case 'create_ticket':
        case 'update_ticket':
            return 'ticket-alt';
        case 'create_maintenance':
        case 'update_maintenance':
            return 'tools';
        case 'update_profile':
            return 'user-edit';
        case 'change_password':
            return 'key';
        default:
            return 'info-circle';
    }
}

/**
 * Get activity color
 *
 * @param string $type The activity type
 * @return string The color class
 */
function getActivityColor($type) {
    switch ($type) {
        case 'login':
            return 'success';
        case 'logout':
            return 'secondary';
        case 'create_ticket':
            return 'danger';
        case 'update_ticket':
            return 'warning';
        case 'create_maintenance':
        case 'update_maintenance':
            return 'info';
        case 'update_profile':
            return 'info';
        case 'change_password':
            return 'warning';
        default:
            return 'secondary';
    }
}

/**
 * Get maintenance status icon
 *
 * @param string $status The maintenance status
 * @return string The icon class
 */
function getMaintenanceStatusIcon($status) {
    switch ($status) {
        case 'scheduled':
            return 'calendar-alt';
        case 'in_progress':
            return 'cog';
        case 'completed':
            return 'check-circle';
        case 'overdue':
            return 'exclamation-triangle';
        case 'cancelled':
            return 'times-circle';
        case 'partial':
            return 'clock';
        case 'failed':
            return 'times';
        case 'postponed':
            return 'pause-circle';
        default:
            return 'question-circle';
    }
}

/**
 * Get ticket status icon
 *
 * @param string $status The ticket status
 * @return string The icon class
 */
function getTicketStatusIcon($status) {
    switch ($status) {
        case 'open':
            return 'folder-open';
        case 'in_progress':
            return 'cog';
        case 'pending':
            return 'clock';
        case 'resolved':
            return 'check-circle';
        case 'closed':
            return 'times-circle';
        case 'cancelled':
            return 'ban';
        default:
            return 'question-circle';
    }
}

/**
 * Get ticket priority color
 *
 * @param string $priority The ticket priority
 * @return string The color class
 */
function getTicketPriorityColor($priority) {
    switch ($priority) {
        case 'low':
            return 'success';
        case 'medium':
            return 'warning';
        case 'high':
            return 'danger';
        case 'critical':
            return 'dark';
        default:
            return 'secondary';
    }
}

/**
 * Get device status color
 *
 * @param string $status The device status
 * @return string The color class
 */
function getDeviceStatusColor($status) {
    switch ($status) {
        case 'operational':
            return 'success';
        case 'under_maintenance':
            return 'warning';
        case 'out_of_service':
            return 'danger';
        case 'retired':
            return 'secondary';
        case 'pending_approval':
            return 'info';
        default:
            return 'secondary';
    }
}

/**
 * Calculate next maintenance date based on frequency
 *
 * @param string $lastDate The last maintenance date
 * @param string $frequency The maintenance frequency
 * @return string The next maintenance date
 */
function calculateNextMaintenanceDate($lastDate, $frequency) {
    $date = new DateTime($lastDate);

    switch ($frequency) {
        case 'daily':
            $date->add(new DateInterval('P1D'));
            break;
        case 'weekly':
            $date->add(new DateInterval('P1W'));
            break;
        case 'monthly':
            $date->add(new DateInterval('P1M'));
            break;
        case 'quarterly':
            $date->add(new DateInterval('P3M'));
            break;
        case 'semi_annually':
            $date->add(new DateInterval('P6M'));
            break;
        case 'annually':
            $date->add(new DateInterval('P1Y'));
            break;
        case 'once':
        default:
            return null; // One-time maintenance doesn't have a next date
    }

    return $date->format('Y-m-d');
}

/**
 * Format currency amount
 *
 * @param float $amount The amount to format
 * @param string $currency The currency code
 * @return string The formatted currency
 */
function formatCurrency($amount, $currency = 'USD') {
    return number_format($amount, 2) . ' ' . $currency;
}


